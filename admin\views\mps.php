<?php
/**
 * Provincial Administration Manager - MPs Management View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check user type and get appropriate MPs
$current_user_id = get_current_user_id();
$user_type = get_user_meta($current_user_id, 'provincial_user_type', true);
$is_district_user = ($user_type === 'district');
$is_provincial_user = Provincial_User_Roles::user_has_provincial_access($current_user_id);

// Get MPs based on user type
if ($is_district_user && !current_user_can('manage_options')) {
    // District users see only MPs from their assigned districts
    $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);

    if (!empty($assigned_districts)) {
        // Get all MPs first, then filter by district assignment
        $all_mps = get_posts(array(
            'post_type' => 'esp_mp',
            'numberposts' => -1,
            'post_status' => 'any',
            'orderby' => 'title',
            'order' => 'ASC'
        ));

        // Filter MPs by assigned districts - only show MPs that are linked to assigned districts
        $mps = array();
        foreach ($all_mps as $mp) {
            $mp_district_id = get_post_meta($mp->ID, '_esp_mp_district_id', true);
            // Only include MPs that are linked to one of the assigned districts
            if (!empty($mp_district_id) && in_array($mp_district_id, $assigned_districts)) {
                $mps[] = $mp;
            }
        }
    } else {
        $mps = array(); // No assigned districts
    }
} else {
    // Provincial users, administrators, and anyone with manage_options capability see all MPs
    $mps = get_posts(array(
        'post_type' => 'esp_mp',
        'numberposts' => -1,
        'post_status' => 'any',
        'orderby' => 'title',
        'order' => 'ASC'
    ));
}
?>

<div class="wrap">
    <?php if ($is_district_user && !current_user_can('manage_options')): ?>
        <h1 class="wp-heading-inline"><?php _e('My District MPs', 'esp-admin-manager'); ?></h1>
        <a href="<?php echo admin_url('post-new.php?post_type=esp_mp'); ?>" class="page-title-action"><?php _e('Add New', 'esp-admin-manager'); ?></a>
        <hr class="wp-header-end">

        <?php
        $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
        if (!empty($assigned_districts)):
            $district_names = array();
            foreach ($assigned_districts as $district_id) {
                $district = get_post($district_id);
                if ($district) {
                    $district_names[] = $district->post_title;
                }
            }
            if (!empty($district_names)): ?>
                <div class="notice notice-info">
                    <p><strong><?php _e('Managing MPs for:', 'esp-admin-manager'); ?></strong>
                    <?php echo esc_html(implode(', ', $district_names)); ?></p>
                </div>
            <?php endif;
        endif; ?>
    <?php else: ?>
        <h1 class="wp-heading-inline"><?php _e('Members of Parliament', 'esp-admin-manager'); ?></h1>
        <a href="<?php echo admin_url('post-new.php?post_type=esp_mp'); ?>" class="page-title-action"><?php _e('Add New', 'esp-admin-manager'); ?></a>
        <hr class="wp-header-end">
    <?php endif; ?>

    <?php settings_errors('esp_messages'); ?>

    <?php if (!empty($mps)): ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th scope="col" class="manage-column column-title column-primary"><?php _e('Name', 'esp-admin-manager'); ?></th>
                    <th scope="col" class="manage-column"><?php _e('Electorate', 'esp-admin-manager'); ?></th>
                    <th scope="col" class="manage-column"><?php _e('Party', 'esp-admin-manager'); ?></th>
                    <th scope="col" class="manage-column"><?php _e('District', 'esp-admin-manager'); ?></th>
                    <th scope="col" class="manage-column"><?php _e('Status', 'esp-admin-manager'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($mps as $mp):
                    $electorate = get_post_meta($mp->ID, '_esp_mp_electorate', true);
                    $party = get_post_meta($mp->ID, '_esp_mp_party', true);
                    $district_id = get_post_meta($mp->ID, '_esp_mp_district_id', true);
                    $district_name = '';
                    if ($district_id) {
                        $district = get_post($district_id);
                        if ($district && $district->post_status === 'publish') {
                            $district_name = $district->post_title;
                        }
                    }
                    $message = $mp->post_content; // Message is now from post content
                ?>
                <tr>
                    <td class="title column-title has-row-actions column-primary" data-colname="Name">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <?php if (has_post_thumbnail($mp->ID)): ?>
                                <?php echo get_the_post_thumbnail($mp->ID, array(40, 40), array('style' => 'border-radius: 50%;')); ?>
                            <?php else: ?>
                                <div style="width: 40px; height: 40px; background: #0073aa; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 16px;">
                                    👤
                                </div>
                            <?php endif; ?>
                            <div>
                                <strong>
                                    <a href="<?php echo get_edit_post_link($mp->ID); ?>" class="row-title">
                                        <?php echo esc_html($mp->post_title); ?>
                                    </a>
                                </strong>
                            </div>
                        </div>
                        <div class="row-actions">
                            <span class="edit">
                                <a href="<?php echo get_edit_post_link($mp->ID); ?>"><?php _e('Edit', 'esp-admin-manager'); ?></a>
                            </span>
                            <?php if (!$is_district_user || current_user_can('manage_options')): ?>
                                | <span class="trash">
                                    <a href="<?php echo get_delete_post_link($mp->ID); ?>"
                                       onclick="return confirm('<?php _e('Are you sure you want to delete this MP?', 'esp-admin-manager'); ?>')"
                                       class="submitdelete"><?php _e('Move to Trash', 'esp-admin-manager'); ?></a>
                                </span>
                            <?php endif; ?>
                        </div>
                        <button type="button" class="toggle-row"><span class="screen-reader-text"><?php _e('Show more details', 'esp-admin-manager'); ?></span></button>
                    </td>
                    <td class="column-electorate" data-colname="Electorate">
                        <?php echo esc_html($electorate); ?>
                    </td>
                    <td class="column-party" data-colname="Party">
                        <?php if ($party): ?>
                            <span class="party-badge"><?php echo esc_html($party); ?></span>
                        <?php endif; ?>
                    </td>
                    <td class="column-district" data-colname="District">
                        <?php if ($district_name): ?>
                            <strong><?php echo esc_html($district_name); ?></strong>
                        <?php else: ?>
                            <span style="color: #999; font-style: italic;"><?php _e('No district assigned', 'esp-admin-manager'); ?></span>
                        <?php endif; ?>
                    </td>
                    <td class="column-status" data-colname="Status">
                        <?php if ($mp->post_status === 'publish'): ?>
                            <span class="status-published"><?php _e('Published', 'esp-admin-manager'); ?></span>
                        <?php else: ?>
                            <span class="status-<?php echo esc_attr($mp->post_status); ?>"><?php echo esc_html(ucfirst($mp->post_status)); ?></span>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <div class="notice notice-warning">
            <?php if ($is_district_user && !current_user_can('manage_options')): ?>
                <p><?php _e('No MPs found for your assigned districts. You can add MPs for your districts using the "Add New" button above.', 'esp-admin-manager'); ?></p>
            <?php else: ?>
                <p><?php _e('No MPs found. Click "Add New" to get started.', 'esp-admin-manager'); ?></p>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>

    <!-- Statistics -->
    <div class="esp-form-section">
        <h3><?php _e('MP Statistics', 'esp-admin-manager'); ?></h3>
        <div class="esp-stats-grid">
            <div class="esp-stats-item">
                <label><?php _e('Total MPs', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-secondary);">
                    <?php echo count($mps); ?>
                </div>
            </div>
            
            <div class="esp-stats-item">
                <label><?php _e('Published', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: green;">
                    <?php echo count(array_filter($mps, function($mp) { return $mp->post_status === 'publish'; })); ?>
                </div>
            </div>
            
            <div class="esp-stats-item">
                <label><?php _e('With Photos', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-primary);">
                    <?php echo count(array_filter($mps, function($mp) { return has_post_thumbnail($mp->ID); })); ?>
                </div>
            </div>
            
            <div class="esp-stats-item">
                <label><?php _e('Unique Parties', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-accent);">
                    <?php 
                    $parties = array_unique(array_filter(array_map(function($mp) {
                        return get_post_meta($mp->ID, '_esp_mp_party', true);
                    }, $mps)));
                    echo count($parties);
                    ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Section -->
    <div class="esp-form-section">
        <h3><?php _e('Preview', 'esp-admin-manager'); ?></h3>
        <p><?php _e('This is how the MPs will appear on your website:', 'esp-admin-manager'); ?></p>
        
        <div style="border: 1px solid #ddd; padding: 20px; background: #f9f9f9; border-radius: 8px; max-height: 400px; overflow-y: auto;">
            <?php echo do_shortcode('[esp_mps limit="6"]'); ?>
        </div>
        
        <p style="margin-top: 15px;">
            <strong><?php _e('Shortcode:', 'esp-admin-manager'); ?></strong>
            <code>[dakoii_prov_admin_mps]</code> <?php _e('(Primary)', 'esp-admin-manager'); ?>
            <small style="color: #666; margin-left: 10px;"><?php _e('or', 'esp-admin-manager'); ?> <code>[dakoii_mps]</code>, <code>[esp_mps]</code></small>
        </p>
        <p>
            <?php _e('All MPs Options:', 'esp-admin-manager'); ?><br>
            <code>[dakoii_prov_admin_mps limit="6"]</code> - <?php _e('Show only 6 MPs', 'esp-admin-manager'); ?><br>
            <code>[dakoii_prov_admin_mps show_photos="false"]</code> - <?php _e('Hide photos', 'esp-admin-manager'); ?><br>
            <code>[dakoii_prov_admin_mps show_messages="true"]</code> - <?php _e('Show MP messages (from main content)', 'esp-admin-manager'); ?><br>
            <code>[dakoii_prov_admin_mps show_districts="true"]</code> - <?php _e('Show district names', 'esp-admin-manager'); ?><br>
            <code>[dakoii_prov_admin_mps district_id="123"]</code> - <?php _e('Show only MPs from specific district', 'esp-admin-manager'); ?>
        </p>
        <p>
            <strong><?php _e('Individual MP Shortcodes:', 'esp-admin-manager'); ?></strong><br>
            <code>[dakoii_mp id="123"]</code> - <?php _e('Show single MP with message (from main content)', 'esp-admin-manager'); ?><br>
            <code>[dakoii_mp_only id="123"]</code> - <?php _e('Show single MP without message', 'esp-admin-manager'); ?><br>
            <small style="color: #666;"><?php _e('Replace "123" with the actual MP ID. You can find MP IDs in the edit URL or by hovering over the MP name above.', 'esp-admin-manager'); ?></small>
        </p>
        <p>
            <strong><?php _e('District Filtering Examples:', 'esp-admin-manager'); ?></strong><br>
            <code>[dakoii_mps district_id="456" show_districts="true"]</code> - <?php _e('Show MPs from district 456 with district names', 'esp-admin-manager'); ?><br>
            <small style="color: #666;"><?php _e('Replace "456" with the actual District ID. You can find District IDs in the Districts management section.', 'esp-admin-manager'); ?></small>
        </p>
    </div>

    <!-- Help Section -->
    <div class="esp-help">
        <h4><?php _e('Managing MPs', 'esp-admin-manager'); ?></h4>
        <p><?php _e('Here are some tips for managing MP profiles effectively:', 'esp-admin-manager'); ?></p>
        <ul style="margin: 10px 0 0 20px;">
            <li><?php _e('Keep MP information current, especially after elections', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use high-quality, professional photos for all MPs', 'esp-admin-manager'); ?></li>
            <li><?php _e('Include accurate electorate and party information', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use the main content area for MP messages to constituents', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use the Bio field for biographical information about the MP', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use the "Draft" status for MPs who are not yet confirmed', 'esp-admin-manager'); ?></li>
            <li><?php _e('Regular updates help maintain public trust and transparency', 'esp-admin-manager'); ?></li>
        </ul>
    </div>
</div>
