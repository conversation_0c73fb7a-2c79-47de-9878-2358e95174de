<?php
/**
 * Plugin Name: Dakoii Provincial Administration Manager
 * Plugin URI: https://www.dakoiims.com
 * Description: Provincial Administration Manager - Central management of all provincial data including Governor profile, MPs, districts, statistics, and administrative structure.
 * Version: 1.0.0
 * Author: <PERSON><PERSON>
 * Author URI: https://www.dakoiims.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: esp-admin-manager
 * Domain Path: /languages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('PROVINCIAL_ADMIN_MANAGER_VERSION', '1.0.0');
define('PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('PROVINCIAL_ADMIN_MANAGER_PLUGIN_URL', plugin_dir_url(__FILE__));
define('PROVINCIAL_ADMIN_MANAGER_PLUGIN_FILE', __FILE__);

/**
 * Main Provincial Administration Manager Class
 */
class Provincial_Administration_Manager {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array('Provincial_Administration_Manager', 'uninstall'));
        
        add_action('init', array($this, 'init'));
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
        add_action('wp_enqueue_scripts', array($this, 'public_enqueue_scripts'));
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        require_once PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'includes/class-provincial-post-types.php';
        require_once PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'includes/class-provincial-admin.php';
        require_once PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'includes/class-provincial-frontend.php';
        require_once PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'includes/class-provincial-shortcodes.php';
        require_once PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'includes/class-provincial-meta-boxes.php';
        require_once PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'includes/class-provincial-user-roles.php';
        require_once PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'includes/class-provincial-user-management.php';
        require_once PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'includes/class-provincial-slideshow.php';
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Initialize post types first
        Provincial_Post_Types::get_instance();

        // Initialize user roles and management
        Provincial_User_Roles::get_instance();

        // Initialize slideshow functionality
        Provincial_Slideshow::get_instance();

        // Initialize admin
        if (is_admin()) {
            Provincial_Admin::get_instance();
            Provincial_Meta_Boxes::get_instance();
            Provincial_User_Management::get_instance();
        }

        // Initialize frontend and shortcodes
        Provincial_Frontend::get_instance();
        Provincial_Shortcodes::get_instance();

        // Load text domain
        load_plugin_textdomain('esp-admin-manager', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function admin_enqueue_scripts($hook) {
        // Load on our plugin pages
        if (strpos($hook, 'provincial-admin') !== false) {
            // Enqueue WordPress media uploader
            wp_enqueue_media();

            wp_enqueue_style(
                'provincial-admin-style',
                PROVINCIAL_ADMIN_MANAGER_PLUGIN_URL . 'admin/css/admin-style.css',
                array(),
                PROVINCIAL_ADMIN_MANAGER_VERSION
            );

            wp_enqueue_script(
                'provincial-admin-script',
                PROVINCIAL_ADMIN_MANAGER_PLUGIN_URL . 'admin/js/admin-script.js',
                array('jquery', 'media-upload', 'media-views'),
                PROVINCIAL_ADMIN_MANAGER_VERSION,
                true
            );

            // Localize script for AJAX
            wp_localize_script('provincial-admin-script', 'provincial_admin_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('provincial_admin_nonce'),
                'slideshow_nonce' => wp_create_nonce('esp_slideshow_nonce')
            ));
        }
    }
    
    /**
     * Enqueue public scripts and styles
     */
    public function public_enqueue_scripts() {
        wp_enqueue_style(
            'provincial-public-style',
            PROVINCIAL_ADMIN_MANAGER_PLUGIN_URL . 'public/css/public-style.css',
            array(),
            PROVINCIAL_ADMIN_MANAGER_VERSION
        );

        // Enqueue Leaflet.js for maps
        wp_enqueue_style(
            'leaflet-css',
            'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css',
            array(),
            '1.9.4'
        );

        wp_enqueue_script(
            'leaflet-js',
            'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js',
            array(),
            '1.9.4',
            false // Load in header to ensure it's available
        );

        wp_enqueue_script(
            'provincial-public-script',
            PROVINCIAL_ADMIN_MANAGER_PLUGIN_URL . 'public/js/public-script.js',
            array('jquery', 'leaflet-js'),
            PROVINCIAL_ADMIN_MANAGER_VERSION,
            true
        );
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create custom post types
        Provincial_Post_Types::get_instance()->register_post_types();

        // Create custom user roles
        Provincial_User_Roles::get_instance()->create_roles();

        // Flush rewrite rules
        Provincial_Post_Types::flush_rewrite_rules();
        flush_rewrite_rules();

        // Set default options
        $this->set_default_options();

        // Create slideshow tables
        Provincial_Slideshow::create_tables();

        // Create default data
        $this->create_default_data();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Remove custom user roles
        Provincial_User_Roles::get_instance()->remove_roles();

        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Delete all custom posts
        $post_types = array('esp_governor', 'esp_mp', 'esp_district', 'esp_event', 'esp_news');
        
        foreach ($post_types as $post_type) {
            $posts = get_posts(array(
                'post_type' => $post_type,
                'numberposts' => -1,
                'post_status' => 'any'
            ));
            
            foreach ($posts as $post) {
                wp_delete_post($post->ID, true);
            }
        }
        
        // Delete options
        delete_option('esp_provincial_statistics');
        delete_option('esp_administrative_structure');
        delete_option('esp_contact_information');
        delete_option('esp_admin_manager_version');
    }
    
    /**
     * Set default options
     */
    private function set_default_options() {
        // Provincial statistics
        $default_stats = array(
            'population' => '450,530',
            'area' => '43,426',
            'districts' => '6',
            'llgs' => '41',
            'wards' => '1,287',
            'urban_llgs' => '4'
        );
        add_option('esp_provincial_statistics', $default_stats);
        
        // Contact information
        $default_contact = array(
            'address' => 'Provincial Headquarters, Papua New Guinea, PO Box 280',
            'phone' => '(+*************',
            'fax' => '(+*************',
            'emergency' => '000',
            'email' => '<EMAIL>',
            'admin_email' => '<EMAIL>',
            'website' => 'www.provincial.gov.pg',
            'office_hours' => 'Monday - Friday, 8:00 AM - 4:30 PM, Closed Weekends & Public Holidays'
        );
        add_option('esp_contact_information', $default_contact);
        
        // Version
        add_option('esp_admin_manager_version', PROVINCIAL_ADMIN_MANAGER_VERSION);
    }
    
    /**
     * Create default data
     */
    private function create_default_data() {
        // Create Governor profile
        $this->create_default_governor();
        
        // Create MP profiles
        $this->create_default_mps();
        
        // Create Districts
        $this->create_default_districts();
        
        // Create sample events and news
        $this->create_sample_events();
        $this->create_sample_news();
    }
    
    /**
     * Create default governor profile
     */
    private function create_default_governor() {
        $governor_exists = get_posts(array(
            'post_type' => 'esp_governor',
            'numberposts' => 1
        ));
        
        if (empty($governor_exists)) {
            $governor_id = wp_insert_post(array(
                'post_title' => 'Hon. Allan Bird',
                'post_content' => 'As we work together to build a prosperous Province, I am committed to ensuring that our government serves every citizen with integrity, transparency, and dedication. Our rich cultural heritage and natural resources are the foundation upon which we will build a sustainable future for our children and generations to come. Through unity, hard work, and good governance, we will achieve our vision of a progressive and self-reliant Province.',
                'post_status' => 'publish',
                'post_type' => 'esp_governor'
            ));
            
            if ($governor_id) {
                update_post_meta($governor_id, '_esp_governor_title', 'Governor of the Province');
                update_post_meta($governor_id, '_esp_governor_party', 'Pangu Party');
                update_post_meta($governor_id, '_esp_governor_email', '<EMAIL>');
                update_post_meta($governor_id, '_esp_governor_phone', '(+*************');
            }
        }
    }
    
    /**
     * Create default MP profiles
     */
    private function create_default_mps() {
        $mps_data = array(
            array(
                'name' => 'Hon. Allan Bird',
                'electorate' => 'Provincial',
                'party' => 'Pangu Party',
                'district' => '' // Provincial MP - no specific district
            ),
            array(
                'name' => 'Hon. Gabriel Kapris',
                'electorate' => 'Wewak Open',
                'party' => 'PNC',
                'district' => 'Wewak'
            ),
            array(
                'name' => 'Hon. Johnson Wamp',
                'electorate' => 'Angoram Open',
                'party' => 'NAP',
                'district' => 'Angoram'
            ),
            array(
                'name' => 'Hon. Samson Knos',
                'electorate' => 'Maprik Open',
                'party' => 'PLP',
                'district' => 'Maprik'
            ),
            array(
                'name' => 'Hon. Joseph Yopyyopy',
                'electorate' => 'Ambunti-Drekikier Open',
                'party' => 'PANGU',
                'district' => 'Ambunti-Drekikier'
            ),
            array(
                'name' => 'Hon. Bernard Hagoria',
                'electorate' => 'Yangoru-Saussia Open',
                'party' => 'URP',
                'district' => 'Yangoru-Saussia'
            )
        );
        
        foreach ($mps_data as $mp_data) {
            $mp_exists = get_posts(array(
                'post_type' => 'esp_mp',
                'title' => $mp_data['name'],
                'numberposts' => 1
            ));
            
            if (empty($mp_exists)) {
                $mp_id = wp_insert_post(array(
                    'post_title' => $mp_data['name'],
                    'post_content' => 'Committed to serving the people of ' . $mp_data['electorate'] . ' and working towards the development of our province.',
                    'post_status' => 'publish',
                    'post_type' => 'esp_mp'
                ));
                
                if ($mp_id) {
                    update_post_meta($mp_id, '_esp_mp_electorate', $mp_data['electorate']);
                    update_post_meta($mp_id, '_esp_mp_party', $mp_data['party']);

                    // Link to district if specified
                    if (!empty($mp_data['district'])) {
                        $district = get_posts(array(
                            'post_type' => 'esp_district',
                            'title' => $mp_data['district'],
                            'numberposts' => 1,
                            'post_status' => 'publish'
                        ));

                        if (!empty($district)) {
                            update_post_meta($mp_id, '_esp_mp_district_id', $district[0]->ID);
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Create default districts
     */
    private function create_default_districts() {
        $districts_data = array(
            array(
                'name' => 'Wewak District',
                'llgs' => '8',
                'wards' => '156',
                'description' => 'Provincial capital and administrative center. Home to the provincial government headquarters and major commercial activities.'
            ),
            array(
                'name' => 'Maprik District',
                'llgs' => '7',
                'wards' => '198',
                'description' => 'Known for its rich cultural heritage and traditional art. Major agricultural area with significant yam and sweet potato production.'
            ),
            array(
                'name' => 'Angoram District',
                'llgs' => '6',
                'wards' => '287',
                'description' => 'Located along the Sepik River. Famous for traditional crocodile initiation ceremonies and wood carving artistry.'
            ),
            array(
                'name' => 'Ambunti-Drekikier District',
                'llgs' => '7',
                'wards' => '195',
                'description' => 'Remote district along the upper Sepik River. Rich in biodiversity and traditional customs of river communities.'
            ),
            array(
                'name' => 'Yangoru-Saussia District',
                'llgs' => '6',
                'wards' => '234',
                'description' => 'Highland district known for its cool climate and coffee production. Important educational center with several schools.'
            ),
            array(
                'name' => 'Dreikikir District',
                'llgs' => '7',
                'wards' => '217',
                'description' => 'Mountainous district with diverse ethnic groups. Important for mining activities and timber resources.'
            )
        );
        
        foreach ($districts_data as $district_data) {
            $district_exists = get_posts(array(
                'post_type' => 'esp_district',
                'title' => $district_data['name'],
                'numberposts' => 1
            ));
            
            if (empty($district_exists)) {
                $district_id = wp_insert_post(array(
                    'post_title' => $district_data['name'],
                    'post_content' => $district_data['description'],
                    'post_status' => 'publish',
                    'post_type' => 'esp_district'
                ));
                
                if ($district_id) {
                    update_post_meta($district_id, '_esp_district_llgs', $district_data['llgs']);
                    update_post_meta($district_id, '_esp_district_wards', $district_data['wards']);
                }
            }
        }
    }
    
    /**
     * Create sample events
     */
    private function create_sample_events() {
        $events_data = array(
            array(
                'title' => 'East Sepik Cultural Festival',
                'content' => 'Annual celebration of traditional arts, music, and dance featuring all six districts. Venue: Wewak Town.',
                'start_date' => '2025-09-16',
                'end_date' => '2025-09-18',
                'location' => 'Wewak Town'
            ),
            array(
                'title' => 'Provincial Assembly Meeting',
                'content' => 'Quarterly assembly session to discuss budget allocations and development projects.',
                'start_date' => '2025-09-25',
                'end_date' => '',
                'location' => 'Provincial Assembly Hall'
            ),
            array(
                'title' => 'Agriculture & Trade Fair',
                'content' => 'Showcasing local products, farming innovations, and business opportunities.',
                'start_date' => '2025-10-05',
                'end_date' => '2025-10-07',
                'location' => 'Wewak Showgrounds'
            ),
            array(
                'title' => 'Youth Leadership Summit',
                'content' => 'Empowering young leaders across all districts with training and networking opportunities.',
                'start_date' => '2025-10-15',
                'end_date' => '',
                'location' => 'Provincial Conference Center'
            )
        );

        foreach ($events_data as $event_data) {
            $event_exists = get_posts(array(
                'post_type' => 'esp_event',
                'title' => $event_data['title'],
                'numberposts' => 1
            ));

            if (empty($event_exists)) {
                $event_id = wp_insert_post(array(
                    'post_title' => $event_data['title'],
                    'post_content' => $event_data['content'],
                    'post_status' => 'publish',
                    'post_type' => 'esp_event'
                ));

                if ($event_id) {
                    update_post_meta($event_id, '_esp_event_start_date', $event_data['start_date']);
                    update_post_meta($event_id, '_esp_event_end_date', $event_data['end_date']);
                    update_post_meta($event_id, '_esp_event_location', $event_data['location']);
                }
            }
        }
    }

    /**
     * Create sample news
     */
    private function create_sample_news() {
        $news_data = array(
            array(
                'title' => 'K850M Provincial Budget Approved',
                'content' => 'Provincial Assembly approves record budget with focus on infrastructure, health, and education development across all six districts of East Sepik Province.',
                'excerpt' => 'Provincial Assembly approves record budget with focus on infrastructure, health, and education development...',
                'date' => '2025-08-19',
                'source' => 'Provincial Administration',
                'featured' => '1'
            ),
            array(
                'title' => 'Wewak-Maprik Highway 65% Complete',
                'content' => 'Major road reconstruction project ahead of schedule, expected completion by December 2025. The highway will improve connectivity between the provincial capital and Maprik District.',
                'excerpt' => 'Major road reconstruction project ahead of schedule, expected completion by December 2025...',
                'date' => '2025-08-18',
                'source' => 'Department of Works',
                'featured' => '1'
            ),
            array(
                'title' => 'New Health Centers Opened in Remote Areas',
                'content' => 'Three new aid posts established in Ambunti-Drekikier and Angoram districts improving healthcare access for rural communities.',
                'excerpt' => 'Three new aid posts established in Ambunti-Drekikier and Angoram districts improving healthcare access...',
                'date' => '2025-08-16',
                'source' => 'Department of Health',
                'featured' => '0'
            ),
            array(
                'title' => 'Provincial Development Grants Now Open',
                'content' => 'Applications accepted until September 15 for community projects, small business support, and infrastructure development initiatives.',
                'excerpt' => 'Applications accepted until September 15 for community projects, small business support, and infrastructure...',
                'date' => '2025-08-15',
                'source' => 'Department of Finance',
                'featured' => '0'
            )
        );

        foreach ($news_data as $news_item) {
            $news_exists = get_posts(array(
                'post_type' => 'esp_news',
                'title' => $news_item['title'],
                'numberposts' => 1
            ));

            if (empty($news_exists)) {
                $news_id = wp_insert_post(array(
                    'post_title' => $news_item['title'],
                    'post_content' => $news_item['content'],
                    'post_excerpt' => $news_item['excerpt'],
                    'post_status' => 'publish',
                    'post_type' => 'esp_news'
                ));

                if ($news_id) {
                    update_post_meta($news_id, '_esp_news_date', $news_item['date']);
                    update_post_meta($news_id, '_esp_news_source', $news_item['source']);
                    update_post_meta($news_id, '_esp_news_featured', $news_item['featured']);
                }
            }
        }
    }
}

// Initialize the plugin
Provincial_Administration_Manager::get_instance();
